"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { ArrowLeft, ArrowRight, Building, Clock, Shield, Scale } from "lucide-react"
import type { FormData } from "@/types/contract"

interface AdvancedSettingsStepProps {
  formData: FormData
  updateFormData: (data: Partial<FormData>) => void
  onNext: () => void
  onPrev: () => void
}

export default function AdvancedSettingsStep({ formData, updateFormData, onNext, onPrev }: AdvancedSettingsStepProps) {
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-base">
            <Building className="h-5 w-5 mr-2 text-green-500" />
            Kontraktdetaljer
          </CardTitle>
          <CardDescription>Bedriftsinformasjon og juridiske bestemmelser</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="companyName">Bedriftsnavn</Label>
              <Input
                id="companyName"
                value={formData.companyName}
                onChange={(e) => updateFormData({ companyName: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="companyOrgNumber">Organisasjonsnummer</Label>
              <Input
                id="companyOrgNumber"
                value={formData.companyOrgNumber}
                onChange={(e) => updateFormData({ companyOrgNumber: e.target.value })}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="companyAddress">Bedriftsadresse</Label>
            <Input
              id="companyAddress"
              value={formData.companyAddress}
              onChange={(e) => updateFormData({ companyAddress: e.target.value })}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-base">
            <Clock className="h-5 w-5 mr-2 text-green-500" />
            Arbeidstid og godtgjørelser
          </CardTitle>
          <CardDescription>Arbeidstidsordning og tilleggsgodtgjørelser</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="workingHoursPerWeek">Timer per uke</Label>
              <Input
                id="workingHoursPerWeek"
                type="number"
                value={formData.workingHoursPerWeek}
                onChange={(e) => updateFormData({ workingHoursPerWeek: Number(e.target.value) })}
                step="0.5"
              />
            </div>
            <div>
              <Label htmlFor="workingTime">Arbeidstid</Label>
              <Input
                id="workingTime"
                value={formData.workingTime}
                onChange={(e) => updateFormData({ workingTime: e.target.value })}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="breakTime">Pauseordning</Label>
            <Input
              id="breakTime"
              value={formData.breakTime}
              onChange={(e) => updateFormData({ breakTime: e.target.value })}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="overtimeRate">Overtidstillegg (%)</Label>
              <Input
                id="overtimeRate"
                type="number"
                value={formData.overtimeRate}
                onChange={(e) => updateFormData({ overtimeRate: Number(e.target.value) })}
                min="40"
              />
            </div>
            <div>
              <Label htmlFor="paymentDay">Lønnsutbetaling (dag i mnd)</Label>
              <Input
                id="paymentDay"
                type="number"
                min="1"
                max="31"
                value={formData.paymentDay}
                onChange={(e) => updateFormData({ paymentDay: Number(e.target.value) })}
                placeholder="5"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="toolAllowance">Verktøygodtgjørelse</Label>
            <Input
              id="toolAllowance"
              value={formData.toolAllowance}
              onChange={(e) => updateFormData({ toolAllowance: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="travelAllowance">Kjøregodtgjørelse</Label>
            <Input
              id="travelAllowance"
              value={formData.travelAllowance}
              onChange={(e) => updateFormData({ travelAllowance: e.target.value })}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-base">
            <Shield className="h-5 w-5 mr-2 text-green-500" />
            Pensjon og forsikring
          </CardTitle>
          <CardDescription>Pensjons- og forsikringsordninger</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="pensionProvider">Pensjonsleverandør</Label>
              <Input
                id="pensionProvider"
                value={formData.pensionProvider}
                onChange={(e) => updateFormData({ pensionProvider: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="pensionOrgNumber">Pensjon org.nr</Label>
              <Input
                id="pensionOrgNumber"
                value={formData.pensionOrgNumber}
                onChange={(e) => updateFormData({ pensionOrgNumber: e.target.value })}
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="insuranceProvider">Forsikringsleverandør</Label>
              <Input
                id="insuranceProvider"
                value={formData.insuranceProvider}
                onChange={(e) => updateFormData({ insuranceProvider: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="insuranceOrgNumber">Forsikring org.nr</Label>
              <Input
                id="insuranceOrgNumber"
                value={formData.insuranceOrgNumber}
                onChange={(e) => updateFormData({ insuranceOrgNumber: e.target.value })}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center text-base">
            <Scale className="h-5 w-5 mr-2 text-green-500" />
            Juridiske bestemmelser
          </CardTitle>
          <CardDescription>Oppsigelsesfrister og andre juridiske forhold</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 pt-0">
          <div>
            <Label htmlFor="noticePeriod">Oppsigelsesfrister</Label>
            <Input
              id="noticePeriod"
              value={formData.noticePeriod}
              onChange={(e) => updateFormData({ noticePeriod: e.target.value })}
            />
          </div>
          <div>
            <Label htmlFor="contractDuration">Kontraktvarighet (kun midlertidig)</Label>
            <Input
              id="contractDuration"
              value={formData.contractDuration}
              onChange={(e) => updateFormData({ contractDuration: e.target.value })}
              placeholder="F.eks. 6 måneder, til 31.12.2024"
            />
          </div>
          <div>
            <Label htmlFor="notificationRules">Varslingsregler</Label>
            <Textarea
              id="notificationRules"
              value={formData.notificationRules}
              onChange={(e) => updateFormData({ notificationRules: e.target.value })}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrev}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Forrige steg
        </Button>
        <Button onClick={onNext} className="bg-green-500 hover:bg-green-600">
          Neste steg
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  )
}
